<template>
  <div class="container_04181016">
    <audio id="id_audio_2310081000" preload="auto">
      <source
        src="https://cdns.bountech.com/marketfront/file/insurance/cdn/picture/ZAXD/ZAXD01/intro.mp3"
        type="audio/mp3"
      />
    </audio>
    <img :src="imgList.img_1" class="header">
    <UserForm :orderInfo="orderInfo" class="form" @onPolicy="checkPolicyDetail" @onClickConfirm="onClickConfirm"></UserForm>
    <van-tabs v-model="activeTabIndex" class="tab" color="#00B667" background="#F7F8F9" line-width="30" line-height="4">
      <van-tab>
        <template #title>
          <div class="tab-title">
            值得信赖
          </div>
        </template>
      </van-tab>
    </van-tabs>
    <div class="introduce">
      <img :src="imgList.img_4">
    </div>
    <img :src="imgList.img_5" class="introduce">
    <div v-if="bottomButtonVisible" class="bottomButton" @click="onClickConfirm">
      <img :src="imgList.img_bottom_button">
    </div>
    <div class="bottom">
    </div>
    <PolicyPopup :obj="policyObj" @ok="onAcceptPolicy"></PolicyPopup>
    <HHPrevPopup :obj="backObj" @onClick="onBackProduct"></HHPrevPopup>
    <HHWarnPopup :obj="warnObj"></HHWarnPopup>
    <!--验证码弹窗-->
    <HHSendCodePopup ref="sendCodeRef" :obj="sendCodeObj" :orderInfo="orderInfo" @onRegister="onRegister"></HHSendCodePopup>
    <!--同意弹窗-->
    <HHAcceptPopup :obj="acceptObj" @onConfirm="onAcceptPolicy" @onPolicy="checkPolicyDetail"></HHAcceptPopup>
    <!--开场动画-->
    <SplashPopup :obj="splashObj" @close="handleSplashClose"></SplashPopup>
  </div>
</template>

<script>
import { checkPhoneNumber, isIos, isMaskedAndT1Phone, isWeixin, url_safe_b64_encode } from "@/utils/Tools";
import img_1 from '@/assets/imgs/ZAXD/ZAXD01/img01_2.webp';
import img_4 from '@/assets/imgs/ZAXD/ZAXD01/img02_3.webp';
import img_5 from '@/assets/imgs/ZAXD/ZAXD01/img03.webp';
import img_bottom_button from '@/assets/imgs/ZAXD/ZAXD01/bottom-button.webp';
import UserForm from "./components/UserForm5";
import PolicyPopup from "./components/PolicyPopup";
import HHPrevPopup from "./components/HHPrevPopup";
import HHWarnPopup from "./components/HHWarnPopup";
import { actionTracking, createOrderInfo, getAppDownloadUrl, loadOrderInfo, saveOrderInfo, showMessage } from "./src";
import { fetchPhoneNumber, ZAXDRegisterNew, ZAXDWXOfficialAuth } from "@/ApiTools/api";
import HHSendCodePopup from './components/HHSendCodePopup2.vue';
import SplashPopup from './components/SplashPopup.vue';
import HHAcceptPopup from './components/HHAcceptPopup.vue';

export default {
  name: "Index3",
  components: { HHWarnPopup, HHPrevPopup, PolicyPopup, UserForm, HHSendCodePopup, HHAcceptPopup, SplashPopup, },
  data() {
    const orderInfo = createOrderInfo();
    return {
      orderInfo,
      scrollTracked: false,
      policyObj: { visible: false, name: '' },
      backObj: { visible: false, name: '' },
      warnObj: { visible: false, },
      sendCodeObj: { visible: false, },
      acceptObj: { visible: false, },
      splashObj: { visible: false, },
      imgList: { img_1, img_4, img_5, img_bottom_button },
      bottomButtonVisible: false,
      isQzd: false,
      activeTabIndex: 0,
    };
  },

  mounted() {
    const query = this.$route.query || {};
    const channel = (query.channel || '1') + '';

    const channelList_2 = Array(200).fill(1).map((_, idx) => {
      return '168774413' + `${8826 + idx}`;
    });

    this.isQzd = channelList_2.includes(channel);

    this.init();

    if (history.scrollRestoration) {
      history.scrollRestoration = 'manual';
    }

    this.$nextTick(() => { // 监听滚动事件
      this.buttonIntersectObserve();
      window.addEventListener('scroll', this.scrollHandler);
    });

    this.$nextTick(() => { // 监听页面返回
      this.pushHistoryState();
      window.addEventListener("popstate", this.popStateHandler,);
    });

    // 显示开场动画
    this.splashObj.visible = true;

    // 测试验证码弹窗，仅开发调试时使用
    // this.sendCodeObj.visible = true;

    // 播放旁白音频
    this.makeAudioWork();
  },

  beforeDestroy() {
    window.removeEventListener('scroll', this.scrollHandler);
    window.removeEventListener('popstate', this.popStateHandler);
  },

  methods: {
    makeAudioWork() {
      const func = () => {
        try {
          const oAudio = document.querySelector('#id_audio_2310081000');
          if (oAudio && !oAudio.alreadyPlay) {
            oAudio.play().then(() => {
              oAudio.alreadyPlay = true;
            });
          }
        } catch (e) {

        }
      };

      window.onclick = func;
      window.ontouchstart = func;
    },

    init() {
      const query = this.$route.query || {};
      query.registerType = query.registerType || '1';

      const localData = loadOrderInfo();
      Object.assign(this.orderInfo, localData, query);
      this.orderInfo.platform = isIos() ? 'ios' : isWeixin() ? 'wechat' : 'android';
      this.orderInfo.page = 'ZAXD01Index3';
      this.orderInfo.verifyCode = '';
      this.orderInfo.checked = true;

      if (!this.orderInfo.openId || !this.orderInfo.appId || !this.orderInfo.unionId) {
        const { code, appId } = query;
        if (appId && code) {
          const params = { channel: this.orderInfo.channel, appId, code };
          ZAXDWXOfficialAuth(params).then(r => {
            const { code, data } = r || {};
            if (code == 2000) {
              const { openId, unionId } = data || {};
              this.orderInfo.appId = appId;
              this.orderInfo.openId = openId;
              this.orderInfo.unionId = unionId;
              saveOrderInfo(this.orderInfo);
            }
          });
        }
      }

      this.fetchPhoneNumber();
    },

    fetchPhoneNumber() {
      const { m, mobile, maskedPhone, mTel } = this.orderInfo;
      if (!m || checkPhoneNumber(mobile) || isMaskedAndT1Phone(maskedPhone, mTel)) {
        return this.performanceTracking();
      }

      const params = { encryptContent: m };
      fetchPhoneNumber(params).then(res => {
        const { encryptPhone, showPhone } = res;
        if (isMaskedAndT1Phone(showPhone, encryptPhone)) {
          this.orderInfo.mTel = encryptPhone;
          // this.orderInfo.maskedPhone = showPhone;
          // this.orderInfo.mobile = showPhone;
          saveOrderInfo(this.orderInfo);
        }
      }).finally(() => {
        return this.performanceTracking();
      });
    },

    handleSplashClose(isAuto) {
        const msg = isAuto ? '自动关闭开场动画' : '手动关闭开场动画';
        this._actionTracking(msg);
    },

    // 点击查看额度按钮
    onClickConfirm() {
      // 如果没勾选协议，弹出同意弹窗
      // 如果勾选协议，进入判断手机号是否合法和获取验证码的流程
      if (this.orderInfo.checked) {
        this.onSendCode();
      } else {
        this.acceptObj.visible = true;
      }
    },

    onSendCode() {
      const { mobile, mTel, maskedPhone } = this.orderInfo;
      // 无论新老用户，都需要显示加载弹窗和验证码弹窗
      // 放到验证码弹窗组件里判断新老用户，再决定是获取验证码还是注册，这里不要立即注册

      if (!checkPhoneNumber(mobile) && !isMaskedAndT1Phone(maskedPhone, mTel)) {
        return showMessage('请输入正确的手机号码');
      }
      saveOrderInfo(this.orderInfo);

      // 验证码弹窗组件里，监听显示状态
      // 当显示时，判断用户是新老用户，新用户发送验证码，老用户直接回调注册
      this.sendCodeObj.visible = true;
    },

    onAcceptPolicy() {
      this.orderInfo.checked = true;
      if (this.sendCodeObj.visible) {
        this.onRegister();
      } else {
        this.onSendCode();
      }
    },

    onRegister() {
      const {
        page,
        infoKey,
        channel,
        mobile,
        platform,
        verifyCode,
        insuranceChannelId,
        maskedPhone,
        mTel,
        checked,
        appId,
        openId,
        unionId,
        registerType,
      } = this.orderInfo;
      const { referrerFlag, coopinfo, corId, referrerName } = this.$route.query || {};
      const flag = coopinfo || referrerFlag;

      if (!checkPhoneNumber(mobile) && !isMaskedAndT1Phone(maskedPhone, mTel)) {
        return showMessage('请填写正确的手机号码');
      }

      if (this.orderInfo.appLink) {
        showMessage('您已是我们的注册用户，立即前往众安贷客户端进行借款');
        setTimeout(() => {
          this.downloadApp(this.orderInfo.appLink);
        }, 500);
        return this._actionTracking('点击缓存注册按钮');
      }

      if (verifyCode.length < 4 || verifyCode.length > 6) {
        return showMessage('请填写正确的验证码');
      }

      if (!checked) {
        return this.checkPolicyDetail('用户授权协议');
      }

      this._actionTracking('点击注册按钮');

      this.$toast.loading({
        message: '额度查询中...',
        duration: 0,
        forbidClick: true,
      });

      const params = {
        page,
        infoKey,
        channel,
        mobile: checkPhoneNumber(mobile) ? mobile : mTel,
        operatorMobile: mTel || (checkPhoneNumber(mobile) ? mobile : ""),
        platform,
        verifyCode,
        referrerFlag: flag,
        corId,
        referrerName,
      };

      if (appId && openId) {
        params.appId = appId;
        params.openId = openId;
        params.unionId = unionId;
      }
      if (insuranceChannelId) {
        params['insuranceChannelId'] = insuranceChannelId;
      }
      if (registerType == 2) {
        params['registerType'] = registerType;
      }
      ZAXDRegisterNew(params).then(res => {
        this.$toast.clear(true);
        const { code, data, msg } = res || {};
        if (code == 2000) {
          const { isRegister, additionalInfo } = data || {};

          if (additionalInfo.downLink) {
            this.orderInfo.appUrlObj = additionalInfo;
            // 保存下载地址
            this.orderInfo.appLink = getAppDownloadUrl(additionalInfo);
            saveOrderInfo(this.orderInfo);
          }


          // isRegister 0 老用户 1 新用户
          isRegister
            ? showMessage('恭喜您，注册成功')
            : showMessage('您已是我们的注册用户，立即前往众安贷客户端进行借款');
          return setTimeout(() => {
            this.downloadApp(this.orderInfo.appLink, isRegister);
          }, 500);
        }

        const message = (msg || '').replace('failure_', '');
        return showMessage(message);
      }).catch(err => {
        return showMessage(JSON.stringify(err));
      });
    },

    downloadApp(link, isRegister = 0) {
      this._actionTracking('跳转结果页');

      const { mobile = '', mTel = '' } = this.orderInfo;
      const params = {
        mobile,
        mTel,
        isRegister,
      };
      const bizParams = url_safe_b64_encode(JSON.stringify(params));

      return this.$router.replace({ name: 'ZAXD01Result', query: { bizParams } });
    },

    fetchBackProduct() {
      this.backObj.visible = true;
    },

    onBackProduct() {
      this.backObj.visible = false;
    },

    checkPolicyDetail(policy) {
      this.policyObj.visible = true;
      this.policyObj.name = policy;
    },

    performanceTracking() {
      this._actionTracking('首页');

      const host = window.location.host;
      this._actionTracking(`域名(${host})`);
    },

    _actionTracking(name, time = 0) {
      actionTracking(this.orderInfo, name, time);
    },

    scrollHandler() {
      //垂直滚动的值兼容问题
      const scrollTopE = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
      // console.log(`滚动 => `, scrollTopE);

      if (!this.scrollTracked && scrollTopE > 200) {
        this.scrollTracked = true;
        this._actionTracking('滚动页面');
      }
    },

    buttonIntersectObserve() {
      if (window.IntersectionObserver) {
        const observer = new IntersectionObserver((entries, observer) => {
          this.bottomButtonVisible = !entries[0].isIntersecting;
        }, { threshold: 0.10 });

        const buttonNode = document.getElementById('id_action_button');
        buttonNode && observer.observe(buttonNode);
      }
    },

    pushHistoryState() {
      if (!window.history.state || window.history.state.a != 1) {
        window.history.pushState({ a: 1 }, null, '');
      }
    },

    popStateHandler() {
      this._actionTracking('点击首页返回按钮');
      this.fetchBackProduct();
    },
  },
};
</script>

<style lang="less" scoped type="text/less">
.container_04181016 {
  margin: 0 auto;
  padding-bottom: 1px;
  width: 3.75rem;
  min-height: 100%;
  font-size: 0.15rem;
  background: #f7f8f9;

  .header {
    display: block;
    width: 100%;
  }

  .form {
    position: relative;
    width: 3.75rem;
    margin: 0 auto;
  }

  .tab {
    padding-top: 0;
  }

  .tab-title {
    font-family: AlibabaPuHuiTi_2_75_SemiBold;
    font-size: 19px;
    font-weight: 600;
    color: #1d243e;
    line-height: 20px;
  }

  .introduce {
    display: block;
    width: 3.75rem;
    margin: 0.16rem 0 0;

    img {
      display: block;
      width: 3.45rem;
      height: auto;
      margin: 0 auto;
    }
  }

  .bottom {
    width: 100%;
    height: .77rem;
    background-color: #fff;
  }

  .bottomButton {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: auto;
    padding: .04rem 0 0 0;
    background-color: #fff;

    img {
      display: block;
      width: 3.75rem;
      height: .77rem;
    }
  }
}
</style>
